{% layout '_LayoutFixedPage' %}

{% section headfile %}

{% assign Header= '/Themes/'| append: theme | append:'/Header' %}
{%include Header-%}
{% assign orderHeader= '/Themes/'| append: theme | append:'/Order/orderHeader' %}
{%include orderHeader-%}

<link href="/assets/font/global/iconfont.css" rel="stylesheet" type="text/css">

<link href="/assets/css/global.css" rel="stylesheet" type="text/css">
<link href="/static/css/global.css" rel="stylesheet" type="text/css">
<link href="/assets/css/cart.css" rel="stylesheet" type="text/css">
<link href="/assets/css/effect.css" rel="stylesheet" type="text/css">
<!--<link href="/assets/js/plugin/slick/slick.css" rel="stylesheet" type="text/css">-->

<script type="text/javascript" src="/assets/js/jquery-1.7.2.min.js"></script>
<script src="/assets/js/global.js"></script>
<script src="/assets/js/cart.js"></script>
<script src="/static/js/global.js"></script>
<script src="/assets/js/shopExpansion.js"></script>
<!--<script src="/assets/js/plugin/slick/slick.js"></script>-->
<script type="text/javascript" src="/assets/js/plugin/tool_tips/tool_tips_web.js"></script>
<script type="text/javascript" src="/assets/js/plugin/jquery-mask-js/jquery.mask.min.js"></script>

{% endsection %}



<div id="success_container">
    <style type="text/css">
        .information_payment .icon_shipping_title {
            background-color: #ff5555;
        }

        .information_box .box_title {
            height: 58px;
        }

        .information_payment .payment_row .check > input:checked:after {
            width: 16px;
            height: 16px;
            border: 5px solid var(--CheckoutMainColor);
        }

        .order_summary .product .product_image .product_qty {
            width: 22px;
        }
    </style>

    <script type="text/javascript">
        if (typeof $(window).webDisplay == "function") {
            $(window).resize(function () { $(window).webDisplay(0); })
            $(document).ready(function () { $(window).webDisplay(0); });
        }
        var shop_config = {};
        var ueeshop_handle = {};
        var lang_obj = {};
    </script>

    <script type="text/javascript">
        window.ShippingInfo = { };

        $(document).ready(() => {

            //设置数据
            getOrderConf();

            function getOrderConf() {
                $.get('/order/GetOrderConf', '', function (data) {

                    data = JSON.parse(data);
                    window.ShippingInfo = data.ShippingInfo;
                    //console.log(data.memberPointsConfig);
                    lang_obj = JSON.parse(data.lang_obj);
                    shop_config = data.shop_config;
                    cart_obj.success_init();

                }, 'json');
            }
        })
    </script>
    <div id="lib_cart" class="wide success_container">

        <div class="success_info checkout_container">

            <div class="checkout_content">
                <div class="information_box information_payment">
                    <div class="box_title">{{"checkout.checkout.paymethod"|translate}}</div>
                    <div class="box_tips">{{"checkout.checkout.secureTips"|translate}}</div>
                    <div class="box_content pay_content">
                        {% for payment in Model.Paymethod %}
                        {% if payment.Method=='Paypal' %}
                        <div class="payment_list clearfix">
                            <div class="payment_row" value="{{payment.PId}}" min="{{payment.MinPrice}}" max="{{payment.MaxPrice}}" no-max-limit="{{payment.NoMaxLimit}}" method="{{payment.Method}}" data-showtype="{{payment.ShowType}}" data-system-type="{{payment.SystemType}}" data-action-method="{{payment.ActionMethod}}">
                                <div class="check"><input name="PId" type="radio"></div>
                                <div class="img"><img src="/assets/images/cart/payment/Paypal.png" alt="PayPal" class="img_loading"><span></span></div>
                            </div>
                        </div>
                        <div class="payment_contents clearfix">
                            <div class="payment_note" data-id="{{payment.PId}}" data-num="1" data-fee="{{payment.AdditionalFee}}">
                                <div class="ext_txt">
                                    <p>{{payment.Description_en|raw}}</p>
                                </div>
                            </div>
                        </div>
                        {% elsif  payment.Method=='Payoneer' or  payment.Method=='OceanPayment' %}
                        <div class="payment_list clearfix">

                            {% assign cardList = payment.Card | remove: "[" | remove: "]" | remove: '"' | split: "," %}

                            <div class="payment_card">
                                <div class="card_box">
                                    {% for cd in cardList %}
                                    {{cd}}
                                    <img src="/assets/images/cart/payment/icon_{{cd}}.png" class="img_loading">
                                    {% endfor %}

                                </div>
                            </div>
                            <div class="payment_row" value="{{payment.PId}}" min="{{payment.MinPrice}}" max="{{payment.MaxPrice}}" no-max-limit="{{payment.NoMaxLimit}}" method="{{payment.Method}}" data-showtype="{{payment.ShowType}}" data-system-type="{{payment.SystemType}}" data-action-method="{{payment.ActionMethod}}">
                                <div class="check"><input name="PId" type="radio"></div>
                                <div class="name_txt">{{"checkout.checkout.CreditOrDebit"|translate}} {{payment.Method}}</div>
                            </div>
                        </div>
                        <div class="payment_contents clearfix">
                            <div class="payment_note" data-id="{{payment.PId}}" data-num="1" data-fee="{{payment.AdditionalFee}}">
                                <div class="ext_txt">{{payment.Description_en|raw}}</div>
                            </div>
                        </div>
                        {% endif %}

                        {% endfor %}

                    </div>
                    <div class="pay_address_error"></div>
                </div>
            </div>
            <div class="order_summary_box">
                <form name="pay_edit_form" method="post" action="/account/">
                    <div class="box_payment order_summary">
                        <div class="products_box information_box">
                            <div class="order_summary_section_content">
                                <table class="product_table">
                                    <thead>
                                        <tr>
                                            <th scope="col"><span class="visually-hidden"></span></th>
                                            <th scope="col"><span class="visually-hidden"></span></th>
                                            <th scope="col"><span class="visually-hidden"></span></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in Model.CartData %}
                                        {% assign url="javascript:;" %}
                                        {% if product.product!=null %}
                                        {% assign url=product.product.PageUrl %}
                                        {% endif %}
                                        <tr class="product" data-cid="0">
                                            <td class="product_image">
                                                <div class="thumbnail">
                                                    <a href="/products/{{url}}" title="{{product.Name}}" class="pic_box"><img src="{{product.PicPath}}?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120" alt="{{product.Name}}" class="img_loading"></a>
                                                    <span class="product_qty notranslate">{{product.Qty}}</span>
                                                </div>
                                            </td>
                                            <td class="product_description">
                                                <span class="product_description_name">{{product.Name}}</span>
                                                <div class="global_pro_info_text">
                                                    {% if product.SKU!="" %}
                                                    <div class="psku"><b>SKU:</b> {{product.SKU}}</div>
                                                    {% endif %}

                                                    {% if product.Property!="{}" and product.Property!="" %}
                                                    {% assign property = product.Property|jsonparse %}
                                                    <div class="attr_box">
                                                        {% for prop in property %}
                                                        <p class="attr_{{prop[0]}}"><b>{{prop[0]}}</b>: {{prop[1]}}</p>
                                                        {% endfor %}
                                                    </div>
                                                    {% endif %}

                                                    <!--<div class="attr_box"><p class="attr_Overseas"><b>{{"checkout.checkout.shipsFrom"|translate}}</b>: {{product.Overseas}}</p></div>-->
                                                    <div class="custom_attr"></div>
                                                </div>

                                                {% if product.BuyType==5 %}
                                                <div class="gifts_tips">
                                                    <i class="iconfont icon-gift1"></i>
                                                    {{"cart.global.free_gift"|translate}}
                                                </div>
                                                {% endif %}
                                            </td>
                                            <td class="product_price notranslate">{{product.itemTotalFormat.Item2}}</td>
                                        </tr>
                                        {% endfor %}

                                    </tbody>
                                </table>
                            </div>

                        </div>

                        <div class="amount_box information_box">
                            <div class="rows clearfix">
                                <div class="flex_box">
                                    <div class="name">{{"web.global.subtotal"|translate}}:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span>{{Model.ordersPriceFormat.ProductPrice}}</span></div>
                                </div>
                            </div>
                            {% if Model.ordersPrice.DiscountPrice>0 %}
                            <div class="rows clearfix" id="DiscountCharge">
                                <div class="flex_box">
                                    <div class="name">Discount:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span>{{Model.ordersPriceFormat.DiscountPrice}}</span></div>
                                </div>
                            </div>
                            {% endif %}


                            {% if Model.ordersPrice.CouponPrice >0 %}
                            <div class="rows clearfix" id="">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.checkout.code_save"|translate}}:</div>
                                    <div class="value notranslate">-<em>{{Model.Symbol}}</em><span>{{Model.ordersPriceFormat.CouponPrice}}</span></div>
                                </div>
                            </div>
                            {% endif %}

                            {% if Model.ordersPrice.PointsPrice>0 %}
                            <div class="rows clearfix" id="">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.points.points"|translate}}:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span>{{Model.ordersPriceFormat.PointsPrice}}</span></div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="rows clearfix">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.checkout.shipcharge"|translate}}:</div>
                                    <div class="value notranslate">
                                        <em>{{Model.Symbol}}</em><span>
                                            {{Model.ordersPriceFormat.ShippingPrice}}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {% if Model.ordersPrice.TaxPrice>0 %}
                            <div class="rows clearfix" id="">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.checkout.tax"|translate}}:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span>{{Model.ordersPriceFormat.TaxPrice}}</span></div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="rows clearfix" id="ServiceCharge">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.checkout.fee"|translate}}:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span id="ot_fee" data-fee="{{Model.ordersPrice.FeePrice}}">{{Model.ordersPriceFormat.FeePrice}}</span></div>
                                </div>
                            </div>
                            <div class="rows total_charge clearfix" id="TotalCharge">
                                <div class="flex_box">
                                    <div class="name">{{"checkout.checkout.grandTotal"|translate}}:</div>
                                    <div class="value notranslate"><em>{{Model.Symbol}}</em><span id="ot_total" class="ot_total" data-amount="{{Model.ordersPrice.TotalPrice}}">{{Model.ordersPriceFormat.TotalPrice}}</span></div>
                                </div>
                            </div>
                            <div class="button">
                                <input type="button" value="Continue" class="btn_place_order btn_global sys_shadow_button btn_coutinue" id="pay_button" style="display: inline-block;">
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="OId" value="{{Model.OId}}">
                    <input type="hidden" name="TotalPrice" value="{{Model.ordersPrice.TotalPrice}}">
                    <input type="hidden" name="Symbols" value="{{Model.Symbol}}" currency="{{Model.OrderCurrency}}">
                    <input type="hidden" name="PaymentId" value="{{Model.PaymentId}}">
                    <input type="hidden" name="PId" value="{{Model.PaymentId}}">
                    <input type="hidden" name="Paymethod" value="{{Model.order.PaymentMethod}}" />
                </form>
            </div>
        </div>
    </div>
</div>



{% assign Footer= '/Themes/'| append: theme | append:'/Footer' %}
{%include Footer-%}