using Aop.Api.Domain;
using Entitys;
using Flurl.Http;
using MessagePack;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Logging;
using NetTaste;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StackExchange.Redis;
using System.Data;
using System.Numerics;
using System.Runtime.Intrinsics.X86;
using System.Security.Cryptography;
using System.Xml.Linq;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.Ext;
using YseStore.IService.Order;
using YseStore.IService.Pay;
using YseStore.IService.Shopping;
using YseStore.Model.FromBody;
using YseStore.Model.VM;
using YseStore.Model.VM.Payment;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Service.Order;
using YseStore.Service.Pay;

namespace YseStore.Controllers
{
    /// <summary>
    /// 支付控制器
    /// </summary>
    public class PaymentController : BaseController
    {
        public readonly IService.Pay.IPaymentService _paymentsServices;
        public readonly IOrderService _orderServices;
        public readonly IOrderProductsService _orderProductsService;
        public readonly IPayPalServices _payPalServices;
        public readonly IOrderLogService _orderLogServices;
        public readonly IPayoneerServices _payoneerServices;
        public readonly ILogger<PaymentController> _loger;
        public readonly IOrderPaymentInfoService _orderPaymentInfoService;
        public readonly IOrderRefundService _orderRefundService;
        public readonly IStringLocalizer<PaymentController> T;
        private readonly IShoppingCartServices _cartServices;
        private readonly IOceanPaymentService _oceanPaymentService;
        private readonly ICaching _caching;

        public PaymentController(IService.Pay.IPaymentService paymentsServices, IOrderProductsService orderProductsService, IPayPalServices payPalServices, IOrderLogService orderLogService, IPayoneerServices payoneerServices, ILogger<PaymentController> loger, IOrderService orderService, IOrderPaymentInfoService orderPaymentInfoService, IOrderRefundService orderRefundService, IStringLocalizer<PaymentController> t, IShoppingCartServices cartServices, IOceanPaymentService oceanPaymentService, ICaching caching)
        {
            _paymentsServices = paymentsServices;
            _orderProductsService = orderProductsService;
            _payPalServices = payPalServices;
            _orderLogServices = orderLogService;
            _payoneerServices = payoneerServices;
            _loger = loger;
            _orderServices = orderService;
            _orderPaymentInfoService = orderPaymentInfoService;
            _orderRefundService = orderRefundService;
            T = t;
            _cartServices = cartServices;
            _oceanPaymentService = oceanPaymentService;
            _caching = caching;
        }

        public IActionResult Index()
        {
            return View();
        }


        #region 获取支付方式列表==================================================
        /// <summary>
        /// 获取支付方式列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetList()
        {

            var list = await _paymentsServices.GetUsedPaymentAsync();

            return PartialView(list);

        }
        #endregion


        #region paypal支付
        /// <summary>
        /// 调用Paypal支付
        /// </summary>
        /// <param name="OId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> PaypalPay()
        {
            var jm = new WebApiCallBack<PayPalApiCallBack>();
            string OId = Request.GetFormString("OId");

            try
            {
                _loger.LogInformation($"PayPal支付：订单号{OId}");

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == OId);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Json(jm);
                }

                if (order.PaymentStatus != "unpaid")
                {
                    jm.status = false;
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                    return Json(jm);
                }


                var paypalPayment = await _paymentsServices.GetUsedPaymentAsync("Paypal");
                if (paypalPayment == null)
                {
                    jm.status = false;
                    jm.msg = T["checkout.result.errorTitle"];//已付款过
                    return Json(jm);

                }


                order.PaymentMethod = "Paypal";
                order.PId = paypalPayment.PId;
                order.PayAdditionalFee = paypalPayment.AdditionalFee;
                order.PayAdditionalAffix = paypalPayment.AffixPrice;

                //更新订单支付方式
                var updateOrder = await _orderServices.Update(order);


                //获取订单商品列表
                var orderProducts = await _orderProductsService.Query(it => it.OrderId == order.OrderId);
                var r = await _payPalServices.PubPay(order, orderProducts);
                if (r.Status)
                {

                    //去支付
                    var logOption = _orderServices.WriteOrderLog("toPay", order);

                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = $"{order.ShippingFirstName} {order.ShippingLastName}",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|Paypal|{r.PayPalResp.id}|跳转到支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "pending",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);

                    jm.data = r;


                }
                else
                {
                    jm.msg = T[r.Message];
                }

                jm.otherData = OId.ToBase64();
                jm.status = r.Status;
            }
            catch (Exception ex)
            {
                jm.status = false;
                jm.msg = ex.Message;
                jm.otherData = OId.ToBase64();
                _loger.LogError(ex, "PaypalPay error: " + ex.Message);
            }

            return Json(jm);
        }


        /// <summary>
        /// paypal 支付回调
        /// 回调后调用 PaypalCapture 才能真正支付
        /// </summary>
        /// <returns></returns>
        [Route("/api/payments/PayPalCallback")]
        [AllowAnonymous]
        public async Task<IActionResult> PayPalCallback()
        {
            var jm = new WebApiCallBack();
            try
            {
                string payerId = Request.GetQueryString("PayerID");
                _loger.LogInformation($"payerID:{payerId}");

                var orderId = Request.GetQueryString("orderId");
                //var uuid = Request.Query["uuid"];
                var cancel = Request.GetQueryString("cancel");
                var paypalId = Request.GetQueryString("token");
                var requestId = Request.GetQueryString("requestId");//请求id

                _loger.LogInformation($"PayPal支付回调：{Request.QueryString.ToString()}");

                //更新uuid
                var order = await _orderServices.QueryByClauseAsync(it => it.OId == orderId);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}");
                    //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                if (cancel == "true")
                {
                    //取消支付 订单记录
                    var logOption = _orderServices.WriteOrderLog("cancelPay", order);

                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|{paypalId}|{orderId}|取消支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "unpaid",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    //取消支付
                    jm.status = false;
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                //调用capture
                PayPalApiCallBack captureRes = await _payPalServices.Capture(paypalId, requestId);
                if (captureRes.Status == false) //付款失败
                {
                    //更新paypal请求id
                    order.PayPalRequestId = Guid.NewGuid().ToUUID();
                    //更新uuid
                    await _orderServices.Update(order);

                    jm.status = false;
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                if (order.PaymentStatus != "unpaid")//已支付
                {
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                    jm.status = true;
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                string queryString = Request.QueryString.ToString();
                //支付回调
                PayPalCallBack model = new PayPalCallBack()
                {
                    cancel = cancel,
                    OrderNo = orderId,
                    PaymentMethod = GlobalConstVars.PayPal,
                    PaypalOrderId = paypalId,
                    uuid = requestId,
                    OrderInfo = queryString,
                    callback = captureRes,
                };

                //封装返回数据
                var r = await _payPalServices.PayPalcallback(model);
                if (r.PaymentStatus == false)
                {
                    //
                    jm.status = false;
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }


                JObject jobj = new JObject();
                jobj.Add("currency", r.FeeCurrency);
                jobj.Add("amount", r.Fee);
                jobj.Add("paysn", r.TradeNo);
                jobj.Add("paypal_state", r.PayPalResult.paypal_state);
                jobj.Add("payer_payment_method", r.PayPalResult.payer_payment_method);
                jobj.Add("paypal_payment_mode", r.PayPalResult.paypal_payment_mode);
                jobj.Add("paypal_payment_mode_state", r.PayPalResult.paypal_payment_mode_state);
                jobj.Add("payInfo", captureRes.ToJson());
                jobj.Add("paypal_capturesId", r.capturesId);

                //支付完成 修改订单
                jm = await _orderPaymentInfoService.Pay(r.OrderNo, order.PaymentMethod, order.UserId ?? 0, jobj, Request.GetIP(), CurrentLang);

                //if (jm.status)
                //{
                //更新用户标签
                //await _userServices.UpdateUserLabel(order.Email, "orderCount", 1);

                ////发邮件
                //await _emailServices.SendPayOrderEmail(order);
                //}

                //跳转取消支付页面
                return Redirect($"/order/complete?orderId={order.OId}");

            }
            catch (Exception ex)
            {
                jm.status = false;
                jm.msg = ex.Message;

                _loger.LogError(ex, "PayPalCallback error: " + ex.Message);
                string orderId = Request.GetQueryString("orderId");
                return Redirect($"/cart/{orderId}/Info?code={orderId.ToBase64()}");
                //return Redirect($"/order/cancel?orderId={Request.GetQueryString("orderId")}");
            }


        }






        #endregion



        #region  payoneer
        #region payoneer支付
        /// <summary>
        /// payoneer 发起支付
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> PayoneerPubPay()
        {
            var jm = new WebApiCallBack<PayoneerApiCallback>();
            try
            {
                string OId = Request.GetFormString("OId");
                _loger.LogInformation($"payoneer支付：订单号{OId}");

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == OId);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Json(jm);
                }

                if (order.PaymentStatus != "unpaid")
                {
                    jm.status = false;
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已经付款过
                    return Json(jm);
                }

                //payoneer支付方式
                var paypalPayment = await _paymentsServices.GetUsedPaymentAsync("Payoneer");
                if (paypalPayment == null)
                {
                    jm.status = false;
                    jm.msg = T["checkout.result.errorTitle"];//已付款过
                    return Json(jm);
                }


                order.PaymentMethod = "Payoneer";
                order.PId = paypalPayment.PId;
                order.PayAdditionalFee = paypalPayment.AdditionalFee;
                order.PayAdditionalAffix = paypalPayment.AffixPrice;

                //更新订单支付方式
                var updateOrder = await _orderServices.Update(order);


                //获取订单商品列表
                var orderProducts = await _orderProductsService.Query(it => it.OrderId == order.OrderId);
                var r = await _payoneerServices.PubPay(order, orderProducts);
                if (r.Status)
                {

                    //去支付
                    var logOption = _orderServices.WriteOrderLog("toPay", order);

                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = $"{order.ShippingFirstName} {order.ShippingLastName}",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|Payoneer|跳转到支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "pending",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    jm.status = r.Status;
                    jm.data = r;
                    jm.otherData = r.redirect;

                    //去支付
                    return Json(jm);
                }
                else
                {
                    jm.msg = r.Message;
                }
                jm.otherData = OId.ToBase64();
                jm.status = r.Status;

                return Json(jm);
            }
            catch (Exception ex)
            {

                jm.status = false;
                jm.msg = ex.Message;
                jm.otherData = Request.GetFormString("OId").ToBase64();
                _loger.LogError(ex, "PayoneerPubPay error: " + ex.Message);
                return Json(jm);

                //jm.status = false;
                //jm.msg = ex.Message;
                //_loger.LogError(ex, "PayoneerPubPay error: " + ex.Message);
                //string orderId = Request.GetQueryString("OId");
                //return Redirect($"/cart/{orderId}/Info?code={orderId.ToBase64()}");

                //return Redirect($"/order/cancel?orderId={Request.GetFormString("OId")}");
            }


        }


        /// <summary>
        /// Payoneer 支付回调
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [Route("/api/payments/PayoneerCallback")]
        public async Task<IActionResult> PayoneerCallback()
        {
            try
            {

                var jm = new WebApiCallBack();

                _loger.LogInformation($"Payoneer支付回调：{Request.QueryString.ToString()}");

                string listUrl = Request.GetQueryString("listUrl");
                string shortId = Request.GetQueryString("shortId");
                string interactionReason = Request.GetQueryString("interactionReason");
                string resultCode = Request.GetQueryString("resultCode");
                string longId = Request.GetQueryString("longId");
                string transactionId = Request.GetQueryString("transactionId");
                string interactionCode = Request.GetQueryString("interactionCode");

                string amount = Request.GetQueryString("amount");
                string currency = Request.GetQueryString("currency");

                string queryString = Request.QueryString.ToString();

                PayoneerCallBack entity = new PayoneerCallBack()
                {
                    longId = longId,
                    transactionId = transactionId,
                    listUrl = listUrl,
                    shortId = shortId,
                    interactionReason = interactionReason,
                    resultCode = resultCode,
                    interactionCode = interactionCode,
                    amount = amount,
                    currency = currency,
                    OrderInfo = queryString,
                };


                var order = await _orderServices.QueryByClauseAsync(it => it.OId == entity.transactionId);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                if (order.PaymentStatus != "unpaid")//已支付
                {
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                    jm.status = true;
                    return Redirect($"/order/complete?orderId={order.OId}");
                }

                //PayoneerCallBack cb = new PayoneerCallBack()
                //{
                //    longId = longId,
                //    transactionId = transactionId,
                //    listUrl = listUrl,
                //    shortId = shortId,
                //    interactionReason = interactionReason,
                //    resultCode = resultCode,
                //    interactionCode = interactionCode,
                //    amount = amount,
                //    currency = currency,
                //};

                //封装回调数据
                var r = await _payoneerServices.Payoneercallback(entity);
                if (r.PaymentStatus == false)
                {
                    //取消支付 订单记录
                    var logOption = _orderServices.WriteOrderLog("cancelPay", order);
                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|{entity.OrderInfo}|取消支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "unpaid",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    //取消支付 订单记录
                    //var orderLog = new OrderLog
                    //{
                    //    orderId = order.orderId,
                    //    userId = order.userId,
                    //    type = (int)GlobalEnumVars.OrderLogTypes.LOG_TYPE_TO_PAY,
                    //    msg = $"{order.userNickName}|{entity.longId}|{entity.OrderInfo}|取消支付",
                    //    data = JsonConvert.SerializeObject(jm),
                    //    createTime = DateTime.Now,
                    //    creater = "",
                    //};
                    //await _orderLogServices.InsertAsync(orderLog);

                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                //计算手续费
                var payFee = await _payoneerServices.GetPayoneerFee(order);

                JObject jobj = new JObject();
                jobj.Add("currency", payFee.Item1);
                jobj.Add("amount", payFee.Item2);
                jobj.Add("paysn", r.TradeNo);
                jobj.Add("payInfo", entity.OrderInfo);
                //支付完成  orders_payment_info

                //支付完成 修改订单
                jm = await _orderPaymentInfoService.Pay(r.OrderNo, order.PaymentMethod, order.UserId ?? 0, jobj, Request.GetIP(), CurrentLang);
                if (jm.status == false)
                {
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }
                //jm = await _billPaymentsServices.Pay(entity.transactionId, order.PaymentMethod, order.UserId, (int)GlobalEnumVars.BillPaymentsType.Order,
                //      jobj);
                //if (jm.status)
                //{
                //    //更新用户标签
                //    await _userServices.UpdateUserLabel(order.Email, "orderCount", 1);

                //    //发邮件
                //    await _emailServices.SendPayOrderEmail(order);
                //}

                //跳转支付成功页面
                return Redirect($"/order/complete?orderId={order.OId}");
            }
            catch (Exception ex)
            {
                var jm = new WebApiCallBack();
                jm.status = false;
                jm.msg = ex.Message;
                _loger.LogError(ex, "PayoneerCallback error: " + ex.Message);

                string orderId = Request.GetQueryString("transactionId");
                return Redirect($"/cart/{orderId}/Info?code={orderId.ToBase64()}");

                //return Redirect($"/order/cancel?orderId={Request.GetQueryString("transactionId")}");
            }

        }

        #endregion





        /// <summary>
        /// 接收 payoneer通知
        /// </summary>
        /// <returns></returns>
        //[Produces("application/json")]
        //[Produces("application/x-www-form-urlencoded; charset=UTF-8")]
        [HttpPost]
        [AllowAnonymous]
        [Route("/api/payments/PayoneerNotification")]
        public async Task<IActionResult> PayoneerNotification()
        {

            //{ "shortId":"11753-51007","amount":"27.96","interactionReason":"OK","resultCode":"00101.WORLDPAY-ACCESS.REFUND-SUBMITTED","longId":"64c0827b04e777054c72191bo","transactionId":"R2023072481093219","referenceId":"64c0827b04e777054c72191bo","resultInfo":"Approved, in process","interactionCode":"PROCEED","network":"VISA","reference":"refund","returnCode":"OK_IN_PROCESS","returnCodeSource":"PSP","returnCodeName":"OK_IN_PROCESS","notificationId":"20631381214401276","currency":"USD","reasonCode":"refund_credited","pspCode":"WORLDPAY","entity":"payment","timestamp":"2023-07-26T04:18:36.518 02:00","statusCode":"paid_out"}

            //{"shortId":"21369-82755","amount":"154.99","interactionReason":"OK","resultCode":"00101.WORLDPAY-ACCESS.REFUND-SUBMITTED","longId":"64d06998feda7525be49a07co","transactionId":"R2023080453378303","referenceId":"64d06998feda7525be49a07co","resultInfo":"Approved, in process","interactionCode":"PROCEED","network":"VISA","reference":"refund","returnCode":"OK_IN_PROCESS","returnCodeSource":"PSP","returnCodeName":"OK_IN_PROCESS","notificationId":"18956950717500873","currency":"USD","reasonCode":"refund_credited","pspCode":"WORLDPAY","entity":"payment","timestamp":"2023-08-07T05:48:40.949 02:00","statusCode":"paid_out"}


            var context = HttpContext;

            var jm = new WebApiCallBack();
            try
            {
                context.Request.ContentType = "application/x-www-form-urlencoded; charset=UTF-8";



                _loger.LogInformation($"Payoneer推送:Form：{context.Request.Form.ToJson()}");
                _loger.LogInformation($"Payoneer推送:Headers：{context.Request.Headers.ToJson()}");
                // 读取请求体
                using var reader = new StreamReader(Request.Body);
                var jsonPayload = await reader.ReadToEndAsync();
                _loger.LogInformation($"Payoneer推送:Body：{jsonPayload}");

                _loger.LogInformation("ContentType:" + context.Request.ContentType);

                //context.Request.ContentType = "application/json";

                var transactionId = context.Request.GetFormString("transactionId");//
                var longId = context.Request.GetFormString("longId");
                var shortId = context.Request.GetFormString("shortId");
                var entityParam = context.Request.GetFormString("entity");
                var statusCode = context.Request.GetFormString("statusCode"); //状态 statusCode=charged  paid_out /charged
                var reasonCode = context.Request.GetFormString("reasonCode");
                var interactionCode = context.Request.GetFormString("interactionCode");
                var interactionReason = context.Request.GetFormString("interactionReason");

                var resultCode = context.Request.GetFormString("resultCode");
                var resultInfo = context.Request.GetFormString("resultInfo");
                var notificationId = context.Request.GetFormString("notificationId");
                var timestamp = context.Request.GetFormString("timestamp");
                var previousStatusCode = context.Request.GetFormString("previousStatusCode");
                var previousReasonCode = context.Request.GetFormString("previousReasonCode");

                //非必填
                var amount = context.Request.GetFormString("amount"); //金额
                var currency = context.Request.GetFormString("currency");//币种
                var reference = context.Request.GetFormString("reference");

          

                _loger.LogInformation($"amount0:{context.Request.Form["amount"]},currency0:{context.Request.Form["currency"]},reference0:{context.Request.Form["reference"]}");
                _loger.LogInformation($"amount:{amount},currency:{currency},reference:{reference}");

                //付款
                if (statusCode == "charged" && interactionReason == "OK")
                {
                    //付款成功
                    PayoneerCallBack entity = new PayoneerCallBack()
                    {
                        longId = longId,
                        transactionId = transactionId,
                        listUrl = "",
                        shortId = shortId,
                        interactionReason = interactionReason,
                        resultCode = resultCode,
                        interactionCode = interactionCode,
                        amount = amount,
                        currency = currency,
                        OrderInfo = context.Request.Form.ToJson(),
                    };


                    var order = await _orderServices.QueryByClauseAsync(it => it.OId == entity.transactionId);//获取订单详情
                    if (order == null)
                    {
                        jm.status = false;
                        jm.msg = T["web.global.orderNotFind"];
                        return Ok("receive-ok");
                    }

                    if (order.PaymentStatus != "unpaid")//已支付
                    {
                        jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                        jm.status = true;
                        return Ok("receive-ok");
                    }


                    //封装回调数据
                    var r = await _payoneerServices.Payoneercallback(entity);
                    if (r.PaymentStatus == false)
                    {
                        //取消支付 订单记录
                        var logOption = _orderServices.WriteOrderLog("cancelPay", order);
                        //订单记录
                        var orderLog = new orders_log
                        {
                            IsAdmin = true,
                            UserId = order.UserId,
                            UserName = "",
                            OrderId = order.OrderId,
                            Log = $"{order.ShippingFirstName} {order.ShippingLastName}|{entity.OrderInfo}|取消支付",
                            LogManage = logOption.LogTitle,
                            LogData = logOption.LogData,
                            PaymentStatus = "unpaid",
                            ShippingStatus = order.ShippingStatus,
                            AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                            Ip = Request.GetIP(),
                            OrderStatus = order.OrderStatus,
                            TId = 0,
                        };
                        await _orderLogServices.AddWithIntId(orderLog);

                        return StatusCode(410);
                    }

                    //计算手续费
                    var payFee = await _payoneerServices.GetPayoneerFee(order);

                    JObject jobj = new JObject();
                    jobj.Add("currency", payFee.Item1);
                    jobj.Add("amount", payFee.Item2);
                    jobj.Add("paysn", r.TradeNo);
                    jobj.Add("payInfo", entity.OrderInfo);
                    //支付完成  orders_payment_info

                    //支付完成 修改订单
                    jm = await _orderPaymentInfoService.Pay(r.OrderNo, order.PaymentMethod, order.UserId ?? 0, jobj, Request.GetIP(), CurrentLang);
                    if (jm.status == false)
                    {
                        return StatusCode(410);
                    }


                    return Ok("receive-ok");


                }


                if (reference == "refund" && interactionReason == "OK") //退款
                {

                    return Ok("receive-ok");

                }




                jm.status = true;
                return Ok("receive-ok");

            }
            catch (Exception ex)
            {
                jm.status = false;
                jm.msg = ex.Message;

                _loger.LogError(ex, ex.Message);

                return StatusCode(410);
            }

        }




        #endregion


        #region 钱海支付
        /// <summary>
        /// 发起钱海支付
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> OceanpaymentPubPay()
        {

            var jm = new WebApiCallBack();
            try
            {
                string OId = Request.GetQueryString("OId");
                if (OId.IsNullOrEmpty())
                {
                    return Redirect("/");
                }

                _loger.LogInformation($"钱海支付：订单号{OId}");
                var order = await _orderServices.QueryByClauseAsync(it => it.OId == OId);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];//订单不存在
                    //return Json(jm);
                    return Redirect("/");
                }

                if (order.PaymentStatus != "unpaid")
                {
                    jm.status = false;
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已经付款过
                                                                      //return Json(jm);

                    return Redirect($"/order/complete?orderId={OId}");
                }

                //OceanPayment支付方式
                var OceanPayment = await _paymentsServices.GetUsedPaymentAsync("OceanPayment");
                if (OceanPayment == null)
                {
                    jm.status = false;
                    jm.msg = T["checkout.result.errorTitle"];//付款方式不存在

                    return Redirect("/");
                }


                order.PaymentMethod = "OceanPayment";
                order.PId = OceanPayment.PId;
                order.PayAdditionalFee = OceanPayment.AdditionalFee;
                order.PayAdditionalAffix = OceanPayment.AffixPrice;

                //更新订单支付方式
                var updateOrder = await _orderServices.Update(order);


                //获取订单商品列表
                var orderProducts = await _orderProductsService.Query(it => it.OrderId == order.OrderId);
                var r = await _oceanPaymentService.PubPay(order, orderProducts);
                if (r.status)
                {

                    //去支付
                    var logOption = _orderServices.WriteOrderLog("toPay", order);

                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = $"{order.ShippingFirstName} {order.ShippingLastName}",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|OceanPayment|跳转到支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "pending",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    jm.status = r.status;
                    jm.data = r.data;
                    jm.otherData = Request.GetQueryString("OId").ToBase64();

                    //去支付
                    //return Json(jm);

                    //去支付
                    return View("oceanpayment", r.data);
                }
                else
                {

                    return Redirect($"/cart/{OId}/Info?code={OId.ToBase64()}");

                    //jm.msg = r.msg;
                    //jm.status = r.status;
                    //return Json(jm);
                }

            }
            catch (Exception ex)
            {

                jm.status = false;
                jm.msg = ex.Message;
                jm.otherData = Request.GetQueryString("OId").ToBase64();
                _loger.LogError(ex, "OceanpaymentPubPay error: " + ex.Message);
                //return Json(jm);

                string orderId = Request.GetQueryString("OId");
                return Redirect($"/cart/{orderId}/Info?code={orderId.ToBase64()}");
            }

        }

        /// <summary>
        /// 钱海回调
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [Route("/api/payments/OceanPaymentCallback")]
        public async Task<IActionResult> OceanPaymentCallback()
        {
            try
            {

                var jm = new WebApiCallBack();

                _loger.LogInformation($"钱海支付回调：{Request.Form.ToJson()}");
                var response_type = Request.GetFormString("response_type");//回调类型
                var account = Request.GetFormString("account");//账户号
                var terminal = Request.GetFormString("terminal");//终端号
                var signValue = Request.GetFormString("signValue");//交易安全签名
                var methods = Request.GetFormString("methods");
                var order_number = Request.GetFormString("order_number");//网站订单号
                var order_currency = Request.GetFormString("order_currency");
                var order_amount = Request.GetFormString("order_amount");
                var order_notes = Request.GetFormString("order_notes");//订单备注信息
                var card_number = Request.GetFormString("card_number");//持卡人的信用卡卡号
                var card_type = Request.GetFormString("card_type"); //卡种  MC  
                var payment_country = Request.GetFormString("payment_country");
                var payment_id = Request.GetFormString("payment_id");//支付ID，Oceanpayment的支付唯一单号
                var payment_Method = Request.GetFormString("payment_Method");
                var payment_authType = Request.GetFormString("payment_authType");
                var payment_status = Request.GetFormString("payment_status");//该笔交易的结果状态  -1:待处理（预授权才会返回） 0:支付失败 1:支付成功
                var payment_details = Request.GetFormString("payment_details");// "50008:Transaction canceled by customer"
                var payment_solutions = Request.GetFormString("payment_solutions"); //付款失败后的解决方法
                var payment_risk = Request.GetFormString("payment_risk"); //未通过的风控规则
                var payment_amount = Request.GetFormDecimal("payment_amount");
                var payment_exchangeRate = Request.GetFormString("payment_exchangeRate");
                var pay_userId = Request.GetFormString("pay_userId");
                var pay_barCode = Request.GetFormString("pay_barCode"); //订单打印码

                var pr = new PayResult();
                pr.OrderInfo = Request.Form.ToJson();
                pr.OrderStatus = payment_status;
                pr.TradeNo = payment_id;
                pr.OrderAmount = order_amount;
                pr.OrderCurrency = order_currency;
                pr.OrderNo = order_number;
                pr.Remark = order_notes;
                pr.RiskInfo = payment_risk;
                //pr.PayMethod = Request.Form["paymentMethod"];


                var payoneerPayment = await _oceanPaymentService.GetOceanPaymentPayment();
                if (payoneerPayment == null)
                {
                    jm.status = false;
                    jm.msg = "OceanPayment支付方式未配置";
                    return Content("数据校验失败！");
                }
                var payoneerAttribute = _oceanPaymentService.GetOceanPaymentConfig(payoneerPayment);

                //验证签名
                var check = _oceanPaymentService.CheckSignValue(payoneerAttribute.Item1, payoneerAttribute.Item2, payoneerAttribute.Item3, signValue, order_number, order_currency, order_amount, order_notes, card_number, payment_id, payment_authType, payment_status, payment_details, payment_risk);
                if (!check)
                {
                    return Content("数据校验失败！");
                }

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == order_number);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                if (order.PaymentStatus != "unpaid")//已支付
                {
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                    jm.status = true;
                    return Redirect($"/order/complete?orderId={order.OId}");
                }



                //封装回调数据
                if (pr.OrderStatus == "0")
                {
                    //取消支付 订单记录
                    var logOption = _orderServices.WriteOrderLog("cancelPay", order);
                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|{pr.OrderNo}|{payment_details}",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "unpaid",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    //_caching.Set();

                    return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                }

                if (pr.OrderStatus == "1" || pr.OrderStatus == "-1")
                {

                    //计算手续费
                    var payFee = await _oceanPaymentService.GeOceanPaymentFee(order, order_amount.ObjToDecimal(), card_type, payment_country);

                    JObject jobj = new JObject();
                    jobj.Add("currency", payFee.Item1);
                    jobj.Add("amount", payFee.Item2);
                    jobj.Add("paysn", pr.TradeNo);
                    jobj.Add("payInfo", Request.Form.ToJson());
                    //支付完成  orders_payment_info

                    //支付完成 修改订单
                    jm = await _orderPaymentInfoService.Pay(pr.OrderNo, order.PaymentMethod, order.UserId ?? 0, jobj, Request.GetIP(), CurrentLang);
                    if (jm.status == false)
                    {
                        return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId={order.OId}");
                    }

                    //跳转支付成功页面
                    return Redirect($"/order/complete?orderId={order.OId}");
                }


                return Redirect($"/cart/{order.OId}/Info?code={order.OId.ToBase64()}"); //return Redirect($"/order/cancel?orderId=
            }
            catch (Exception ex)
            {
                var jm = new WebApiCallBack();
                jm.status = false;
                jm.msg = ex.Message;
                _loger.LogError(ex, "OceanPaymentCallback error: " + ex.Message);

                string orderId = Request.GetQueryString("transactionId");
                return Redirect($"/cart/{orderId}/Info?code={orderId.ToBase64()}");

                //return Redirect($"/order/cancel?orderId={Request.GetQueryString("transactionId")}");
            }

        }


        /// <summary>
        /// 钱海通知URL
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [Consumes("application/xml")]
        [Route("/api/payments/OceanPaymentNotification")]
        public async Task<IActionResult> OceanPaymentNotification([FromBody] XElement xmlData)
        {
            try
            {

                var jm = new WebApiCallBack();

                _loger.LogInformation($"钱海通知回调：{xmlData.ToJson()}");

                var response_type = xmlData.Element("response_type")?.Value;//回调类型
                var account = xmlData.Element("account")?.Value;//账户号
                var terminal = xmlData.Element("terminal")?.Value;//终端号
                var signValue = xmlData.Element("signValue")?.Value;//交易安全签名
                var methods = xmlData.Element("methods")?.Value;
                var order_number = xmlData.Element("order_number")?.Value;//网站订单号
                var card_type = xmlData.Element("card_type")?.Value;
                var card_country = xmlData.Element("card_country")?.Value;//卡属国家
                var order_currency = xmlData.Element("order_currency")?.Value;
                var order_amount = xmlData.Element("order_amount")?.Value;
                var order_notes = xmlData.Element("order_notes")?.Value;//订单备注信息
                var card_number = xmlData.Element("card_number")?.Value;//持卡人的信用卡卡号


                var payment_country = xmlData.Element("payment_country")?.Value;
                var payment_id = xmlData.Element("payment_id")?.Value;//支付ID，Oceanpayment的支付唯一单号
                var payment_authType = xmlData.Element("payment_authType")?.Value;
                var payment_status = xmlData.Element("payment_status")?.Value;//该笔交易的结果状态  -1:待处理（预授权才会返回） 0:支付失败 1:支付成功
                var payment_details = xmlData.Element("payment_details")?.Value;
                var payment_solutions = xmlData.Element("payment_solutions")?.Value; //付款失败后的解决方法
                var payment_risk = xmlData.Element("payment_risk")?.Value; //未通过的风控规则


                //var payment_Method = Request.Form["payment_Method"];
                var payment_amount = xmlData.Element("payment_amount")?.Value; //消费者实际付款金额
                var payment_exchangeRate = xmlData.Element("payment_exchangeRate")?.Value; //汇率
                var pay_userId = xmlData.Element("pay_userId")?.Value;//消费者ID
                var pay_barCode = xmlData.Element("pay_barCode")?.Value; //订单打印码

                var pr = new PayResult();
                pr.OrderInfo = Request.Form.ToJson();
                pr.OrderStatus = payment_status;
                pr.TradeNo = payment_id;
                pr.OrderAmount = order_amount;
                pr.OrderCurrency = order_currency;
                pr.OrderNo = order_number;
                pr.Remark = order_notes;
                pr.RiskInfo = payment_risk;
                //pr.PayMethod = Request.Form["paymentMethod"];


                var payoneerPayment = await _oceanPaymentService.GetOceanPaymentPayment();
                if (payoneerPayment == null)
                {
                    jm.status = false;
                    jm.msg = "OceanPayment支付方式未配置";
                    return Content("false");
                }
                var payoneerAttribute = _oceanPaymentService.GetOceanPaymentConfig(payoneerPayment);

                //验证签名
                var check = _oceanPaymentService.CheckSignValue(payoneerAttribute.Item1, payoneerAttribute.Item2, payoneerAttribute.Item3, signValue, order_number, order_currency, order_amount, order_notes, card_number, payment_id, payment_authType, payment_status, payment_details, payment_risk);
                if (!check)
                {
                    return Content("false");
                }

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == order_number);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Content("false");
                }

                if (order.PaymentStatus != "unpaid")//已支付
                {
                    jm.msg = T["checkout.checkout.already_paid_tips"];//已付款过
                    jm.status = true;

                    return Ok("receive-ok");
                }



                //封装回调数据
                if (pr.OrderStatus != "1")
                {
                    //取消支付 订单记录
                    var logOption = _orderServices.WriteOrderLog("cancelPay", order);
                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{order.ShippingFirstName} {order.ShippingLastName}|{pr.OrderNo}|取消支付",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "unpaid",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);

                    return Content("false");
                }

                //计算手续费
                var payFee = await _oceanPaymentService.GeOceanPaymentFee(order, decimal.Parse(payment_amount), card_type, payment_country);

                JObject jobj = new JObject();
                jobj.Add("currency", payFee.Item1);
                jobj.Add("amount", payFee.Item2);
                jobj.Add("paysn", pr.TradeNo);
                jobj.Add("payInfo", xmlData.ToJson());
                //支付完成  orders_payment_info

                //支付完成 修改订单
                jm = await _orderPaymentInfoService.Pay(pr.OrderNo, order.PaymentMethod, order.UserId ?? 0, jobj, Request.GetIP(), CurrentLang);
                if (jm.status == false)
                {
                    return Content("false");
                }

                return Ok("receive-ok");
            }
            catch (Exception ex)
            {
                var jm = new WebApiCallBack();
                jm.status = false;
                jm.msg = ex.Message;
                _loger.LogError(ex, "OceanPaymentNotification error: " + ex.Message);

                return StatusCode(410);

            }

        }







        #endregion

    }
}
