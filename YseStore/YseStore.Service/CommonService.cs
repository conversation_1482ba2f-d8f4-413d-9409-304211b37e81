using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Security;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.Common;
using I18Next.Net.Plugins;
using Microsoft.Extensions.Logging;
using YseStore.Common.Const;
using System.Security.Cryptography.X509Certificates;

namespace YseStore.Service
{
    /// <summary>
    /// 通用服务实现类
    /// </summary>
    public class CommonService : ICommonService
    {
        private readonly ILogger<CommonService> _logger;
        private readonly ICaching _caching;
        public CommonService(ICaching caching, ILogger<CommonService> logger)
        {
            _caching = caching;
            _logger = logger;
        }

        /// <summary>
        /// 获取阿里云国家编码
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public string GetAliyunCountryCode(string ip)
        {


            return "";

        }

        public static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true;
        }


        public string GetAliyunCountryCodeV1(string ip)
        {

            try
            {
                string ipCount = "ipCount";
                string redisKey = "AliyunAPICountry";
                string redisContentKey = "AliyunAPIContent";

                if (ip.IsNullOrEmpty() || ip == "127.0.0.1")
                {
                    return "";
                }

                //记录ip访问次数
                int count = _caching.HashGet<int>(ipCount, ip);
                count++;
                _caching.HashSet(ipCount, ip, count);


                //从缓存中获取
                var country = _caching.HashGet(redisKey, ip);
                if (!country.IsNullOrEmpty())
                {
                    return country;
                }

                String host = AppSettingsConstVars.IpApiHost;
                String path = AppSettingsConstVars.IpApiPath;
                String method = "GET";
                String appcode = AppSettingsConstVars.IpApiAppCode;


                String querys = $"ip={ip}";
                String bodys = "";
                String url = host + path;
                HttpWebRequest httpRequest = null;
                HttpWebResponse httpResponse = null;

                if (0 < querys.Length)
                {
                    url = url + "?" + querys;
                }

                if (host.Contains("https://"))
                {
                    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
                    httpRequest = (HttpWebRequest)WebRequest.CreateDefault(new Uri(url));
                }
                else
                {
                    httpRequest = (HttpWebRequest)WebRequest.Create(url);
                }
                httpRequest.Method = method;
                httpRequest.Headers.Add("Authorization", "APPCODE " + appcode);
                if (0 < bodys.Length)
                {
                    byte[] data = Encoding.UTF8.GetBytes(bodys);
                    using Stream stream = httpRequest.GetRequestStream();

                    stream.Write(data, 0, data.Length);

                }
                try
                {
                    httpResponse = (HttpWebResponse)httpRequest.GetResponse();
                }
                catch (WebException ex)
                {
                    _logger.LogError(ex, ex.Message);
                    httpResponse = (HttpWebResponse)ex.Response;
                }


                using Stream st = httpResponse.GetResponseStream();
                using StreamReader reader = new StreamReader(st, Encoding.GetEncoding("utf-8"));
                string result = reader.ReadToEnd();
                if (result.IsNullOrEmpty())
                {
                    return "";
                }


                //记录接口返回值
                _caching.HashSet(redisContentKey, ip, result);
                //
                var response = result.JsonToObj<IpResult>();
                if (response == null)
                {
                    return "";
                }
                if (response.code != 100)
                {
                    return "";
                }
                string countryCode = response.result.en_short;

                //记录ip对应国家
                _caching.HashSet(redisKey, ip, countryCode);

                if (!countryCode.IsNullOrEmpty())
                {
                    return countryCode;
                }
                return "";

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                _logger.LogError(ex, "获取阿里云国家编码失败，IP: {ip}", ip);
                return "";
            }

        }

        public string GetAliyunCountryCodeV2(string ip)
        {

            return "";
        }


    }

    public class IpResult
    {
        public int code { get; set; }
        public string message { get; set; }
        public string ip { get; set; }
        public IResult result { get; set; }
    }

    public class IResult
    {
        public string en_short { get; set; }   // 英文简称
        public string en_name { get; set; }    // 归属国家英文名称
        public string nation { get; set; }     // 归属国家
        public string province { get; set; }   // 归属省份
        public string city { get; set; }       // 归属城市
        public string district { get; set; }   // 归属县区
        public int adcode { get; set; }        // 归属地编码
        public double lat { get; set; }        // 纬度
        public double lng { get; set; }        // 经度
    }
}
